# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['lector.py'],
    pathex=[],
    binaries=[],
    datas=[('config.json', '.'), ('preguntas.json', '.'), ('recompensas.json', '.'), ('sistema_adaptativo.py', '.'), ('sistema_recompensas_mejorado.py', '.')],
    hiddenimports=['tkinter', 'tkinter.ttk', 'tkinter.messagebox', 'winsound', 'json', 'random', 'time', 'threading', 'pathlib', 'datetime'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='LectorProgresivo',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='recursos/imagenes/56e38321f1c3a46290c26e2f8f81792a.ico',
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='LectorProgresivo',
)
