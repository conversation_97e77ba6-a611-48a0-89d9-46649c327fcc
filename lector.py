#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Asistente de Aprendizaje de Lectura
Un programa educativo que ayuda a los niños a practicar la lectura
de manera divertida e interactiva.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import sys
import random
import datetime
import threading
import time
from pathlib import Path

# Importar sistemas mejorados
try:
    from sistema_recompensas_mejorado import SistemaRecompensas
    RECOMPENSAS_DISPONIBLE = True
except ImportError:
    RECOMPENSAS_DISPONIBLE = False
    print("Sistema de recompensas no disponible")

# Importaciones para funcionalidades avanzadas
try:
    import keyboard
    KEYBOARD_DISPONIBLE = True
except ImportError:
    KEYBOARD_DISPONIBLE = False
    print("keyboard no está instalado. Bloqueo de teclas deshabilitado.")


class LectorProgresivo:
    def __init__(self):
        self.directorio_base = Path(__file__).parent
        self.cargar_configuracion()
        self.cargar_preguntas()
        self.cargar_progreso()
        
        # Variables de estado
        self.pregunta_actual = None
        self.tiempo_inicio_pregunta = None
        self.bypass_activado = False
        self.respuesta_bloqueada = False  # Para prevenir spam de Enter
        self.intentos_pregunta_actual = 0  # Para inteligencia adaptativa
        self.tiempo_inicio_sesion = time.time()  # Para controlar tiempo de ayuda
        self.fallos_totales_sesion = 0  # Para controlar fallos de ayuda
        self.puede_salir = False  # Para controlar cuándo mostrar botón salir
        self.errores_pregunta_actual = 0  # Para contar errores en la pregunta actual
        self.recibio_ayuda_pregunta_actual = False  # Para saber si recibió ayuda en esta pregunta
        self.respuestas_sin_ayuda = 0  # Para contar respuestas correctas sin ayuda

        # Variables para el contador de preguntas
        self.preguntas_correctas_sesion = 0
        self.preguntas_minimas = self.config['seguridad']['preguntas_minimas_para_salir']
        self.contador_animacion_id = None
        self.preguntas_respondidas_sesion = set()  # IDs de preguntas ya respondidas en esta sesión

        # Inicializar sistema de recompensas
        if RECOMPENSAS_DISPONIBLE:
            self.sistema_recompensas = SistemaRecompensas(self)
        else:
            self.sistema_recompensas = None
        
        # Crear ventana principal
        self.crear_ventana_principal()
        
        # Configurar bloqueo de teclas si está habilitado
        if KEYBOARD_DISPONIBLE and self.config['seguridad']['bloquear_teclas_sistema']:
            self.configurar_bloqueo_teclas()

    def cargar_configuracion(self):
        """Carga la configuración desde config.json"""
        try:
            with open(self.directorio_base / 'config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Error", "No se encontró el archivo config.json")
            sys.exit(1)
        except json.JSONDecodeError:
            messagebox.showerror("Error", "Error al leer config.json")
            sys.exit(1)

    def cargar_preguntas(self):
        """Carga las preguntas desde preguntas.json"""
        try:
            with open(self.directorio_base / 'preguntas.json', 'r', encoding='utf-8') as f:
                self.preguntas_data = json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Error", "No se encontró el archivo preguntas.json")
            sys.exit(1)

    def cargar_progreso(self):
        """Carga el progreso del usuario desde respuestas_guardadas.json"""
        try:
            with open(self.directorio_base / 'respuestas_guardadas.json', 'r', encoding='utf-8') as f:
                self.progreso = json.load(f)
        except FileNotFoundError:
            # Si no existe, usar valores por defecto
            self.progreso = {
                "usuario": {
                    "nombre": "",
                    "nivel_actual": 1,
                    "estrellitas_acumuladas": 0,
                    "fecha_ultima_sesion": "",
                    "total_preguntas_respondidas": 0,
                    "total_respuestas_correctas": 0
                },
                "historial_respuestas": [],
                "preguntas_falladas": {},
                "estadisticas": {
                    "tiempo_promedio_respuesta_segundos": 0,
                    "racha_actual_correctas": 0,
                    "mejor_racha": 0,
                    "preguntas_por_nivel": {
                        "1": {"correctas": 0, "incorrectas": 0},
                        "2": {"correctas": 0, "incorrectas": 0},
                        "3": {"correctas": 0, "incorrectas": 0},
                        "4": {"correctas": 0, "incorrectas": 0}
                    }
                }
            }

    def guardar_progreso(self):
        """Guarda el progreso actual en el archivo JSON"""
        try:
            with open(self.directorio_base / 'respuestas_guardadas.json', 'w', encoding='utf-8') as f:
                json.dump(self.progreso, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error al guardar progreso: {e}")



    def crear_ventana_principal(self):
        """Crea la ventana principal fullscreen"""
        self.root = tk.Tk()
        self.root.title("Asistente de Lectura")
        
        # Configurar fullscreen sin botones de cerrar
        self.root.attributes('-fullscreen', True)
        self.root.attributes('-topmost', True)
        self.root.overrideredirect(True)  # Quita la barra de título
        
        # Configurar colores y fuente (más vibrantes y amigables)
        bg_color = '#87CEEB'  # Azul cielo más vibrante
        text_color = '#2F4F4F'  # Verde oscuro
        font_family = 'Comic Sans MS'  # Fuente más infantil
        
        self.root.configure(bg=bg_color)
        
        # Frame principal
        self.main_frame = tk.Frame(self.root, bg=bg_color)
        self.main_frame.pack(fill='both', expand=True, padx=50, pady=50)
        
        # Título
        self.titulo_label = tk.Label(
            self.main_frame,
            text="🌟 ¡Hora de Leer! 🌟",
            font=(font_family, 32, 'bold'),
            fg=text_color,
            bg=bg_color
        )
        self.titulo_label.pack(pady=20)
        
        # Área de pregunta
        self.pregunta_frame = tk.Frame(self.main_frame, bg=bg_color)
        self.pregunta_frame.pack(fill='both', expand=True, pady=20)
        
        # Label para la pregunta
        self.pregunta_label = tk.Label(
            self.pregunta_frame,
            text="",
            font=(font_family, self.config['interfaz']['tamaño_fuente_pregunta'], 'bold'),
            fg=text_color,
            bg=bg_color,
            wraplength=800,
            justify='center'
        )
        self.pregunta_label.pack(pady=30)
        
        # Entry para la respuesta
        self.respuesta_var = tk.StringVar()
        self.respuesta_entry = tk.Entry(
            self.pregunta_frame,
            textvariable=self.respuesta_var,
            font=(font_family, self.config['interfaz']['tamaño_fuente_respuesta']),
            justify='center',
            width=30
        )
        self.respuesta_entry.pack(pady=20)
        self.respuesta_entry.bind('<Return>', self.verificar_respuesta)
        
        # Botones
        self.botones_frame = tk.Frame(self.main_frame, bg=bg_color)
        self.botones_frame.pack(pady=20)
        
        self.btn_responder = tk.Button(
            self.botones_frame,
            text="✓ Responder",
            font=(font_family, 16, 'bold'),
            bg='#90EE90',
            fg='#2F4F4F',
            padx=20,
            pady=10,
            command=lambda: [self.reproducir_sonido_click(), self.verificar_respuesta()]
        )
        self.btn_responder.pack(side='left', padx=10)
        
        self.btn_ayuda = tk.Button(
            self.botones_frame,
            text="💡 Ayuda",
            font=(font_family, 16, 'bold'),
            bg='#FFB6C1',
            fg='#2F4F4F',
            padx=20,
            pady=10,
            command=lambda: [self.reproducir_sonido_click(), self.mostrar_ayuda()]
        )
        self.btn_ayuda.pack(side='left', padx=10)

        # Botón para ver colección de stickers (integrado en ventana principal)
        self.btn_stickers = tk.Button(
            self.botones_frame,
            text="🌟 Mis Stickers",
            font=(font_family, 16, 'bold'),
            bg='#FFD700',
            fg='#2F4F4F',
            padx=20,
            pady=10,
            command=lambda: [self.reproducir_sonido_click(), self.mostrar_stickers_integrado()]
        )
        self.btn_stickers.pack(side='left', padx=10)

        # Botón de salir (inicialmente oculto)
        self.btn_salir = tk.Button(
            self.botones_frame,
            text="🚪 Salir",
            font=(font_family, 20, 'bold'),
            bg='#FF6B6B',
            fg='white',
            padx=30,
            pady=15,
            command=lambda: [self.reproducir_sonido_click(), self.salir_aplicacion()]
        )
        # No se empaqueta inicialmente



        # Área de estrellitas (más grande y colorida)
        self.estrellitas_label = tk.Label(
            self.main_frame,
            text=f"⭐ Estrellitas: {self.progreso['usuario']['estrellitas_acumuladas']}",
            font=(font_family, 20, 'bold'),
            fg='#FFD700',
            bg=bg_color
        )
        self.estrellitas_label.pack(side='bottom', pady=15)
        
        # Bind para detectar bypass de padres
        self.root.bind('<KeyPress>', self.detectar_bypass)
        
        # Enfocar en el entry
        self.respuesta_entry.focus_set()
        
        # Verificar reinicio semanal de recompensas
        if self.sistema_recompensas:
            self.sistema_recompensas.verificar_reinicio_semanal()

        # Crear área para mostrar stickers (inicialmente oculta)
        self.stickers_frame = None

        # Mostrar primera pregunta
        self.mostrar_nueva_pregunta()

    def mostrar_stickers_integrado(self):
        """Muestra/oculta la colección de stickers en la ventana principal"""
        if self.stickers_frame and self.stickers_frame.winfo_exists():
            # Si ya está visible, ocultarlo
            self.stickers_frame.destroy()
            self.stickers_frame = None
            self.btn_stickers.config(text="🌟 Mis Stickers")
        else:
            # Mostrar stickers
            self.crear_area_stickers()
            self.btn_stickers.config(text="❌ Cerrar Stickers")

    def crear_area_stickers(self):
        """Crea el área de stickers en la ventana principal"""
        if not self.sistema_recompensas:
            messagebox.showinfo("Info", "Sistema de stickers no disponible")
            return

        # Crear frame para stickers
        self.stickers_frame = tk.Frame(self.main_frame, bg='#F0F8FF', relief='raised', bd=3)
        self.stickers_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Título
        tk.Label(self.stickers_frame,
                text="🌟 Mi Colección de Stickers 🌟",
                font=('Comic Sans MS', 18, 'bold'),
                fg='#4169E1',
                bg='#F0F8FF').pack(pady=10)

        # Frame scrollable para stickers
        canvas = tk.Canvas(self.stickers_frame, bg='#F0F8FF', height=200)
        scrollbar = tk.Scrollbar(self.stickers_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#F0F8FF')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Mostrar stickers en grid
        row = 0
        col = 0
        for sticker in self.sistema_recompensas.recompensas_data['stickers_disponibles']:
            sticker_frame = tk.Frame(scrollable_frame, bg='white', relief='raised', bd=2)
            sticker_frame.grid(row=row, column=col, padx=5, pady=5, sticky='nsew')

            if sticker['desbloqueado']:
                # Sticker desbloqueado
                tk.Label(sticker_frame,
                        text=sticker['emoji'],
                        font=('Arial', 30),
                        bg='white').pack(pady=5)
                tk.Label(sticker_frame,
                        text=sticker['nombre'],
                        font=('Arial', 8, 'bold'),
                        bg='white').pack()
                tk.Label(sticker_frame,
                        text="✓ Desbloqueado",
                        font=('Arial', 7),
                        fg='green',
                        bg='white').pack(pady=2)
            else:
                # Sticker bloqueado
                tk.Label(sticker_frame,
                        text="🔒",
                        font=('Arial', 30),
                        bg='white').pack(pady=5)
                tk.Label(sticker_frame,
                        text="???",
                        font=('Arial', 8, 'bold'),
                        bg='white').pack()
                tk.Label(sticker_frame,
                        text=f"Necesitas {sticker['costo']} ⭐",
                        font=('Arial', 7),
                        fg='orange',
                        bg='white').pack(pady=2)

            col += 1
            if col >= 4:  # 4 columnas
                col = 0
                row += 1

        canvas.pack(side="left", fill="both", expand=True, padx=5)
        scrollbar.pack(side="right", fill="y")

    def configurar_bloqueo_teclas(self):
        """Configura el bloqueo de teclas del sistema"""
        if not KEYBOARD_DISPONIBLE:
            return
            
        teclas_bloqueadas = self.config['seguridad']['teclas_bloqueadas']
        
        def bloquear_tecla(e):
            return False  # Bloquea la tecla
            
        try:
            for tecla in teclas_bloqueadas:
                keyboard.add_hotkey(tecla, bloquear_tecla, suppress=True)
        except Exception as e:
            print(f"Error al configurar bloqueo de teclas: {e}")

    def obtener_pregunta_aleatoria(self):
        """Obtiene una pregunta aleatoria usando sistema de porcentajes por nivel"""
        nivel_actual = self.progreso['usuario']['nivel_actual']

        # Obtener todas las preguntas disponibles por nivel
        preguntas_por_nivel = self._obtener_preguntas_por_nivel()

        # Filtrar preguntas ya respondidas en esta sesión
        preguntas_disponibles = self._filtrar_preguntas_respondidas(preguntas_por_nivel)

        # Si no hay preguntas disponibles, mostrar mensaje
        if not any(preguntas_disponibles.values()):
            return None

        # Seleccionar nivel usando porcentajes
        nivel_seleccionado = self._seleccionar_nivel_por_porcentaje(nivel_actual, preguntas_disponibles)

        # Seleccionar pregunta aleatoria del nivel seleccionado
        preguntas_nivel = preguntas_disponibles[nivel_seleccionado]
        return random.choice(preguntas_nivel) if preguntas_nivel else None

    def _obtener_preguntas_por_nivel(self):
        """Obtiene todas las preguntas organizadas por nivel"""
        preguntas_por_nivel = {1: [], 2: [], 3: [], 4: []}

        for nivel_key, nivel_data in self.preguntas_data['niveles'].items():
            for pregunta in nivel_data['preguntas']:
                nivel = pregunta['dificultad']
                if nivel in preguntas_por_nivel:
                    preguntas_por_nivel[nivel].append(pregunta)

        return preguntas_por_nivel

    def _filtrar_preguntas_respondidas(self, preguntas_por_nivel):
        """Filtra preguntas ya respondidas en esta sesión"""
        preguntas_filtradas = {1: [], 2: [], 3: [], 4: []}

        for nivel, preguntas in preguntas_por_nivel.items():
            for pregunta in preguntas:
                if pregunta['id'] not in self.preguntas_respondidas_sesion:
                    preguntas_filtradas[nivel].append(pregunta)

        return preguntas_filtradas

    def _seleccionar_nivel_por_porcentaje(self, nivel_actual, preguntas_disponibles):
        """Selecciona un nivel usando porcentajes basados en el nivel actual"""
        # Definir porcentajes por nivel actual
        porcentajes = {
            1: {1: 100},  # Nivel 1: 100% nivel 1
            2: {2: 60, 1: 40},  # Nivel 2: 60% nivel 2, 40% nivel 1
            3: {3: 50, 2: 30, 1: 20},  # Nivel 3: 50% nivel 3, 30% nivel 2, 20% nivel 1
            4: {4: 50, 3: 25, 2: 15, 1: 10}  # Nivel 4: 50% nivel 4, 25% nivel 3, 15% nivel 2, 10% nivel 1
        }

        # Obtener porcentajes para el nivel actual
        porcentajes_nivel = porcentajes.get(nivel_actual, {1: 100})

        # Filtrar solo niveles que tienen preguntas disponibles
        niveles_disponibles = []
        pesos_disponibles = []

        for nivel, peso in porcentajes_nivel.items():
            if preguntas_disponibles.get(nivel, []):
                niveles_disponibles.append(nivel)
                pesos_disponibles.append(peso)

        # Si no hay niveles disponibles, usar nivel 1
        if not niveles_disponibles:
            return 1

        # Selección ponderada
        total_peso = sum(pesos_disponibles)
        punto_aleatorio = random.uniform(0, total_peso)
        peso_acumulado = 0

        for i, peso in enumerate(pesos_disponibles):
            peso_acumulado += peso
            if peso_acumulado >= punto_aleatorio:
                return niveles_disponibles[i]

        # Fallback
        return niveles_disponibles[0]

    def mostrar_nueva_pregunta(self):
        """Muestra una nueva pregunta en la interfaz"""
        self.pregunta_actual = self.obtener_pregunta_aleatoria()

        if not self.pregunta_actual:
            self.pregunta_label.config(text="¡Felicidades! Has respondido todas las preguntas disponibles.\n¡Reinicia la aplicación para jugar de nuevo!")

            # Desbloquear botón de salir automáticamente
            if not self.puede_salir:
                self.puede_salir = True
                self.btn_salir.pack(side='right', padx=20)

            # Reproducir sonido especial de felicitaciones
            self.reproducir_sonido_felicitaciones()

            # Mostrar celebración especial de completado
            if self.sistema_recompensas:
                self.sistema_recompensas.mostrar_pantalla_juego_desbloqueado()

            return

        # Limpiar respuesta anterior
        self.respuesta_var.set("")

        # Mostrar pregunta
        self.pregunta_label.config(text=self.pregunta_actual['pregunta'])

        # Registrar tiempo de inicio
        self.tiempo_inicio_pregunta = time.time()

        # Resetear intentos para la nueva pregunta
        self.intentos_pregunta_actual = 0
        self.errores_pregunta_actual = 0
        self.recibio_ayuda_pregunta_actual = False

        # Desbloquear respuestas
        self.respuesta_bloqueada = False



        # Enfocar en el entry
        self.respuesta_entry.focus_set()

    def mostrar_ayuda(self):
        """Muestra ayuda visual para la pregunta actual (con restricciones)"""
        if not self.pregunta_actual:
            return

        # Verificar si puede usar ayuda
        tiempo_transcurrido = time.time() - self.tiempo_inicio_sesion
        puede_ayuda_tiempo = tiempo_transcurrido >= 300  # 5 minutos
        puede_ayuda_fallos = self.fallos_totales_sesion >= 5  # 5 fallos

        if not (puede_ayuda_tiempo or puede_ayuda_fallos):
            tiempo_restante = int(300 - tiempo_transcurrido)
            fallos_restantes = 5 - self.fallos_totales_sesion
            mensaje = f"💡 La ayuda se desbloquea después de:\n\n"
            mensaje += f"⏰ {tiempo_restante} segundos más\n"
            mensaje += f"❌ {fallos_restantes} fallos más\n\n"
            mensaje += "¡Sigue intentando! 💪"
            self.mostrar_mensaje_temporal(mensaje, color='#FFFFE0')
            return

        respuesta_correcta = self.pregunta_actual['respuesta_esperada']

        # Mostrar la respuesta letra por letra
        self.mostrar_ayuda_letra_por_letra(respuesta_correcta)

    def mostrar_ayuda_letra_por_letra(self, palabra):
        """Muestra la palabra letra por letra como ayuda"""
        ayuda_texto = ""
        for i, letra in enumerate(palabra):
            ayuda_texto += letra + " "

        mensaje = f"💡 Ayuda: {ayuda_texto.strip()}"
        self.mostrar_mensaje_temporal(mensaje, color='#FFFFE0')

    def verificar_respuesta(self, event=None):
        """Verifica si la respuesta del usuario es correcta"""
        if not self.pregunta_actual or self.respuesta_bloqueada:
            return

        respuesta_usuario = self.respuesta_var.get().strip().lower()

        if not respuesta_usuario:
            messagebox.showwarning("Atención", "¡Escribe una respuesta!")
            return

        # Bloquear respuestas para prevenir spam
        self.respuesta_bloqueada = True

        # Incrementar intentos
        self.intentos_pregunta_actual += 1

        # Verificar respuesta (ignorando mayúsculas y espacios extra)
        respuesta_usuario_limpia = respuesta_usuario.strip().lower()
        respuesta_correcta = self.pregunta_actual['respuesta_esperada'].lower().strip()
        alternativas = [alt.lower().strip() for alt in self.pregunta_actual.get('alternativas', [])]

        es_correcta = (respuesta_usuario_limpia == respuesta_correcta or
                      respuesta_usuario_limpia in alternativas)

        # Calcular tiempo de respuesta
        tiempo_respuesta = time.time() - self.tiempo_inicio_pregunta if self.tiempo_inicio_pregunta else 0

        # Registrar respuesta
        self.registrar_respuesta(es_correcta, respuesta_usuario, tiempo_respuesta)

        # Marcar pregunta como respondida en esta sesión (solo si es correcta)
        if es_correcta:
            self.preguntas_respondidas_sesion.add(self.pregunta_actual['id'])
            self.manejar_respuesta_correcta()
        else:
            self.fallos_totales_sesion += 1  # Incrementar contador de fallos
            self.manejar_respuesta_incorrecta()

    def registrar_respuesta(self, es_correcta, respuesta_usuario, tiempo_respuesta):
        """Registra la respuesta en el historial"""
        registro = {
            "fecha": datetime.datetime.now().isoformat(),
            "pregunta_id": self.pregunta_actual['id'],
            "pregunta": self.pregunta_actual['pregunta'],
            "respuesta_usuario": respuesta_usuario,
            "respuesta_correcta": self.pregunta_actual['respuesta_esperada'],
            "es_correcta": es_correcta,
            "tiempo_respuesta_segundos": round(tiempo_respuesta, 2),
            "nivel": self.pregunta_actual['dificultad']
        }

        self.progreso['historial_respuestas'].append(registro)

        # Actualizar estadísticas
        self.progreso['usuario']['total_preguntas_respondidas'] += 1
        if es_correcta:
            self.progreso['usuario']['total_respuestas_correctas'] += 1
            self.progreso['estadisticas']['racha_actual_correctas'] += 1
            if self.progreso['estadisticas']['racha_actual_correctas'] > self.progreso['estadisticas']['mejor_racha']:
                self.progreso['estadisticas']['mejor_racha'] = self.progreso['estadisticas']['racha_actual_correctas']
        else:
            self.progreso['estadisticas']['racha_actual_correctas'] = 0
            # Registrar pregunta fallada
            pregunta_id = str(self.pregunta_actual['id'])
            if pregunta_id not in self.progreso['preguntas_falladas']:
                self.progreso['preguntas_falladas'][pregunta_id] = 0
            self.progreso['preguntas_falladas'][pregunta_id] += 1

        # Actualizar estadísticas por nivel
        nivel_str = str(self.pregunta_actual['dificultad'])
        if nivel_str in self.progreso['estadisticas']['preguntas_por_nivel']:
            if es_correcta:
                self.progreso['estadisticas']['preguntas_por_nivel'][nivel_str]['correctas'] += 1
            else:
                self.progreso['estadisticas']['preguntas_por_nivel'][nivel_str]['incorrectas'] += 1

        # Actualizar fecha de última sesión
        self.progreso['usuario']['fecha_ultima_sesion'] = datetime.datetime.now().isoformat()

        # Guardar progreso
        self.guardar_progreso()

    def manejar_respuesta_correcta(self):
        """Maneja una respuesta correcta"""
        # Agregar estrellita
        self.progreso['usuario']['estrellitas_acumuladas'] += self.config['recompensas']['estrellitas_por_respuesta_correcta']

        # Contar respuestas sin ayuda para el botón de salir
        if not self.recibio_ayuda_pregunta_actual:
            self.respuestas_sin_ayuda += 1

        # Actualizar display de estrellitas
        self.estrellitas_label.config(text=f"⭐ Estrellitas: {self.progreso['usuario']['estrellitas_acumuladas']}")

        # Obtener mensaje de celebración del sistema de recompensas
        if self.sistema_recompensas:
            mensaje = self.sistema_recompensas.obtener_celebracion_aleatoria()
            # Solo verificar stickers, no celebración especial aquí
            self.sistema_recompensas.verificar_nuevos_stickers(self.progreso['usuario']['estrellitas_acumuladas'])
        else:
            mensaje = "¡Correcto! 🎉"

        # Reproducir sonido de respuesta correcta
        self.reproducir_sonido_especial("correcto")

        # Mostrar celebración
        self.mostrar_celebracion(mensaje)

        # Verificar si debe subir de nivel
        self.verificar_subida_nivel()

        # Verificar si debe mostrar botón de salir (solo si no recibió ayuda)
        self.verificar_boton_salir()

        # Programar siguiente pregunta automáticamente
        self.root.after(3000, self.mostrar_nueva_pregunta)

    def manejar_respuesta_incorrecta(self):
        """Maneja una respuesta incorrecta"""
        respuesta_correcta = self.pregunta_actual['respuesta_esperada']
        self.errores_pregunta_actual += 1

        # Sistema de ayuda progresiva
        if self.errores_pregunta_actual == 1:
            mensaje = "¡Inténtalo de nuevo! 😊"
        elif self.errores_pregunta_actual == 2:
            mensaje = f"💡 Pista: La respuesta tiene {len(respuesta_correcta)} letras"
        elif self.errores_pregunta_actual == 3:
            mensaje = f"💡 Pista: Empieza con la letra '{respuesta_correcta[0].upper()}'"
            self.recibio_ayuda_pregunta_actual = True
        else:  # 4 o más errores
            # Mostrar la respuesta completa
            mensaje = f"💡 La respuesta es: {respuesta_correcta}"
            self.recibio_ayuda_pregunta_actual = True

        # Reproducir sonido de respuesta incorrecta
        self.reproducir_sonido_especial("incorrecto")

        self.mostrar_mensaje_temporal(mensaje, color='#FFB6C1')

        # Solo continuar automáticamente si no se mostró la respuesta
        if self.errores_pregunta_actual < 4:
            # Desbloquear respuestas para permitir otro intento
            self.root.after(2000, lambda: setattr(self, 'respuesta_bloqueada', False))
        else:
            # Si se mostró la respuesta, continuar a la siguiente pregunta automáticamente
            self.root.after(4000, self.mostrar_nueva_pregunta)

    def verificar_boton_salir(self):
        """Verifica si debe mostrar el botón de salir (solo si no recibió ayuda)"""
        # Mostrar botón de salir después de 10 respuestas correctas SIN AYUDA o 15 minutos
        tiempo_transcurrido = time.time() - self.tiempo_inicio_sesion

        if ((self.respuestas_sin_ayuda >= 10 or tiempo_transcurrido >= 900) and
            not self.puede_salir):
            self.puede_salir = True
            self.btn_salir.pack(side='right', padx=20)
            self.reproducir_sonido_especial("desbloqueo")

            # Mostrar la celebración especial "Puedes jugar" AQUÍ
            if self.sistema_recompensas:
                self.sistema_recompensas.mostrar_pantalla_juego_desbloqueado()

            if self.respuestas_sin_ayuda >= 10:
                mensaje = "🎉 ¡Botón de salir desbloqueado!\n¡10 respuestas correctas sin ayuda!"
            else:
                mensaje = "🎉 ¡Botón de salir desbloqueado!\n¡Has practicado por 15 minutos!"
            self.mostrar_mensaje_temporal(mensaje, color='#90EE90')

    def salir_aplicacion(self):
        """Sale de la aplicación de forma segura"""
        self.reproducir_sonido_especial("salir")
        respuesta = messagebox.askyesno("Salir", "¿Estás seguro de que quieres salir?\n\n¡Has hecho un gran trabajo! 🌟")
        if respuesta:
            self.cerrar_aplicacion()

    def reproducir_sonido_especial(self, tipo_sonido):
        """Reproduce sonidos especiales para interacciones"""
        try:
            import winsound

            sonidos = {
                "correcto": (800, 200),      # Frecuencia alta, corta
                "incorrecto": (300, 300),    # Frecuencia baja, media
                "celebracion": [(600, 100), (700, 100), (800, 200)],  # Secuencia ascendente
                "sticker": [(500, 100), (600, 100), (700, 100), (800, 200)],  # Secuencia mágica
                "desbloqueo": [(400, 150), (600, 150), (800, 300)],  # Sonido de logro
                "salir": [(800, 100), (600, 100), (400, 200)],  # Sonido de despedida
                "felicitaciones": [  # Sonido épico de felicitaciones
                    (523, 200), (659, 200), (784, 200), (1047, 300),  # Do-Mi-Sol-Do (acorde mayor)
                    (1047, 150), (1175, 150), (1319, 150), (1397, 300),  # Do-Re-Mi-Fa
                    (1397, 200), (1319, 200), (1175, 200), (1047, 400),  # Fa-Mi-Re-Do (descendente)
                    (784, 200), (880, 200), (988, 200), (1047, 600)  # Sol-La-Si-Do (final triunfal)
                ]
            }

            if tipo_sonido in sonidos:
                sonido = sonidos[tipo_sonido]
                if isinstance(sonido, list):
                    # Secuencia de sonidos
                    for freq, duracion in sonido:
                        winsound.Beep(freq, duracion)
                else:
                    # Sonido simple
                    freq, duracion = sonido
                    winsound.Beep(freq, duracion)

        except ImportError:
            # Si winsound no está disponible, usar sonido del sistema
            try:
                import os
                if os.name == 'nt':  # Windows
                    import ctypes
                    ctypes.windll.user32.MessageBeep(0)
            except:
                pass  # Sin sonido si no hay opciones disponibles

    def reproducir_sonido_click(self):
        """Reproduce un sonido suave de click para botones"""
        try:
            import winsound
            winsound.Beep(600, 100)  # Sonido suave y corto
        except ImportError:
            try:
                import os
                if os.name == 'nt':
                    import ctypes
                    ctypes.windll.user32.MessageBeep(-1)  # Sonido por defecto
            except:
                pass

    def reproducir_sonido_felicitaciones(self):
        """Reproduce un sonido épico de felicitaciones por completar todas las preguntas"""
        try:
            import winsound
            import threading

            def reproducir_secuencia():
                # Sonido épico de felicitaciones con melodía triunfal
                secuencia = [
                    (523, 200), (659, 200), (784, 200), (1047, 300),  # Do-Mi-Sol-Do (acorde mayor)
                    (1047, 150), (1175, 150), (1319, 150), (1397, 300),  # Do-Re-Mi-Fa
                    (1397, 200), (1319, 200), (1175, 200), (1047, 400),  # Fa-Mi-Re-Do (descendente)
                    (784, 200), (880, 200), (988, 200), (1047, 600)  # Sol-La-Si-Do (final triunfal)
                ]

                for freq, duracion in secuencia:
                    winsound.Beep(freq, duracion)

            # Reproducir en hilo separado para no bloquear la interfaz
            threading.Thread(target=reproducir_secuencia, daemon=True).start()

        except ImportError:
            # Fallback a sonido del sistema
            try:
                import os
                if os.name == 'nt':
                    import ctypes
                    # Reproducir sonido de exclamación del sistema varias veces
                    for _ in range(3):
                        ctypes.windll.user32.MessageBeep(0x30)  # MB_ICONEXCLAMATION
                        import time
                        time.sleep(0.3)
            except:
                pass

    def mostrar_celebracion(self, mensaje):
        """Muestra una celebración temporal"""
        # Crear ventana de celebración
        celebracion_frame = tk.Frame(self.main_frame, bg='#FFD700', relief='raised', bd=5)
        celebracion_frame.place(relx=0.5, rely=0.5, anchor='center')

        celebracion_label = tk.Label(
            celebracion_frame,
            text=mensaje,
            font=(self.config['interfaz']['fuente_principal'], 28, 'bold'),
            fg='#2F4F4F',
            bg='#FFD700',
            padx=50,
            pady=30
        )
        celebracion_label.pack()



        # Quitar celebración después de unos segundos
        tiempo_celebracion = self.config['interfaz']['tiempo_mostrar_celebracion_segundos'] * 1000
        self.root.after(tiempo_celebracion, celebracion_frame.destroy)

    def mostrar_mensaje_temporal(self, mensaje, color='#FFB6C1'):
        """Muestra un mensaje temporal"""
        mensaje_frame = tk.Frame(self.main_frame, bg=color, relief='raised', bd=3)
        mensaje_frame.place(relx=0.5, rely=0.5, anchor='center')

        mensaje_label = tk.Label(
            mensaje_frame,
            text=mensaje,
            font=(self.config['interfaz']['fuente_principal'], 20, 'bold'),
            fg='#2F4F4F',
            bg=color,
            padx=30,
            pady=20,
            justify='center'
        )
        mensaje_label.pack()

        # Quitar mensaje después de unos segundos
        self.root.after(4000, mensaje_frame.destroy)

    def verificar_subida_nivel(self):
        """Verifica si el usuario debe subir de nivel"""
        nivel_actual = self.progreso['usuario']['nivel_actual']
        preguntas_correctas_nivel = self.progreso['estadisticas']['preguntas_por_nivel'].get(str(nivel_actual), {}).get('correctas', 0)

        preguntas_necesarias = self.config['aprendizaje']['preguntas_para_subir_nivel']

        if preguntas_correctas_nivel >= preguntas_necesarias and nivel_actual < 4:
            self.progreso['usuario']['nivel_actual'] += 1
            self.mostrar_celebracion(f"¡Subiste al nivel {self.progreso['usuario']['nivel_actual']}! 🎊")
            self.guardar_progreso()

    def detectar_bypass(self, event):
        """Detecta si se está ingresando el bypass de padres"""
        if hasattr(self, 'bypass_buffer'):
            self.bypass_buffer += event.char
        else:
            self.bypass_buffer = event.char

        # Mantener solo los últimos caracteres
        if len(self.bypass_buffer) > 10:
            self.bypass_buffer = self.bypass_buffer[-10:]

        # Verificar bypass
        bypass_code = self.config['seguridad']['bypass_padres']
        if bypass_code in self.bypass_buffer:
            self.activar_bypass()

    def activar_bypass(self):
        """Activa el bypass de padres"""
        self.bypass_activado = True
        self.cerrar_aplicacion()

    def cerrar_aplicacion(self):
        """Cierra la aplicación de manera segura"""
        # Guardar progreso final
        self.guardar_progreso()

        # Desactivar bloqueo de teclas
        if KEYBOARD_DISPONIBLE:
            try:
                keyboard.unhook_all()
            except:
                pass

        # Cerrar ventana
        self.root.quit()
        self.root.destroy()

    def ejecutar(self):
        """Ejecuta la aplicación principal"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.cerrar_aplicacion()
        except Exception as e:
            print(f"Error en la aplicación: {e}")
            self.cerrar_aplicacion()


def main():
    """Función principal"""
    try:
        # Verificar argumentos de línea de comandos
        if len(sys.argv) > 1:
            if sys.argv[1] == "--setup-startup":
                # Configurar startup automáticamente
                setup_startup()
                return

        app = LectorProgresivo()
        app.ejecutar()
    except Exception as e:
        print(f"Error al iniciar la aplicación: {e}")
        input("Presiona Enter para salir...")


def setup_startup():
    """Configura el sistema de startup automático"""
    try:
        from startup_manager import StartupManager

        startup_manager = StartupManager()

        print("🔧 Configurando sistema de startup...")

        if startup_manager.register_startup():
            print("✅ Sistema de startup configurado exitosamente")
            print("🎮 El juego se ejecutará automáticamente:")
            print("   - Al iniciar el sistema (después de 5 minutos)")
            print("   - Cada 2 horas si el sistema no está idle")
            print("   - Solo si hay un teclado conectado")
        else:
            print("❌ Error configurando el sistema de startup")

    except ImportError:
        print("❌ Error: No se encontró el módulo startup_manager")
    except Exception as e:
        print(f"❌ Error configurando startup: {e}")


if __name__ == "__main__":
    main()
