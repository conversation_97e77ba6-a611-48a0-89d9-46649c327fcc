# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Análisis del archivo principal
a_lector = Analysis(
    ['lector.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.json', '.'),
        ('preguntas.json', '.'),
        ('recompensas.json', '.'),
        ('*.png', '.'),  # Imágenes si las hay
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'winsound',
        'keyboard',
        'psutil',
        'win32api',
        'win32con',
        'win32file',
        'wmi'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Análisis del gestor de startup
a_startup = Analysis(
    ['startup_manager.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'psutil',
        'win32api',
        'win32con',
        'wmi',
        'winreg'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

# Combinar análisis
MERGE((a_lector, 'lector', 'lector'), (a_startup, 'startup_manager', 'startup_manager'))

# Ejecutable principal (lector)
pyz_lector = PYZ(a_lector.pure, a_lector.zipped_data, cipher=block_cipher)

exe_lector = EXE(
    pyz_lector,
    a_lector.scripts,
    a_lector.binaries,
    a_lector.zipfiles,
    a_lector.datas,
    [],
    name='LectorProgresivo',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Sin ventana de consola
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='recursos/imagenes/56e38321f1c3a46290c26e2f8f81792a.ico',
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)

# Ejecutable del gestor de startup
pyz_startup = PYZ(a_startup.pure, a_startup.zipped_data, cipher=block_cipher)

exe_startup = EXE(
    pyz_startup,
    a_startup.scripts,
    a_startup.binaries,
    a_startup.zipfiles,
    a_startup.datas,
    [],
    name='LectorProgresivo_Startup',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # Con ventana de consola para debug
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
