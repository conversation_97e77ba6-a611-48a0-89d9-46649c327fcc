#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test de las nuevas funciones implementadas:
- Contador de preguntas animado
- Sonido de typing
- Configuración de preguntas mínimas
- Ícono del ejecutable
"""

import tkinter as tk
from tkinter import messagebox
import json
import time
import threading
from pathlib import Path

class TestNuevasFunciones:
    def __init__(self):
        self.directorio_base = Path(__file__).parent
        self.cargar_configuracion()
        self.crear_ventana_test()
        
    def cargar_configuracion(self):
        """Carga la configuración para verificar los nuevos parámetros"""
        try:
            with open(self.directorio_base / 'config.json', 'r', encoding='utf-8') as f:
                self.config = json.load(f)
        except FileNotFoundError:
            messagebox.showerror("Error", "No se encontró config.json")
            return
            
    def crear_ventana_test(self):
        """Crea la ventana de prueba"""
        self.root = tk.Tk()
        self.root.title("Test - Nuevas Funciones")
        self.root.geometry("800x600")
        self.root.configure(bg='#F0F8FF')
        
        # Variables de prueba
        self.preguntas_correctas = 0
        self.preguntas_minimas = self.config['seguridad']['preguntas_minimas_para_salir']
        
        self.crear_interfaz()
        
    def crear_interfaz(self):
        """Crea la interfaz de prueba"""
        # Título
        titulo = tk.Label(
            self.root,
            text="🧪 Test de Nuevas Funciones",
            font=('Comic Sans MS', 20, 'bold'),
            bg='#F0F8FF',
            fg='#2F4F4F'
        )
        titulo.pack(pady=20)
        
        # Contador simulado (esquina superior izquierda)
        self.contador_frame = tk.Frame(self.root, bg='#F0F8FF')
        self.contador_frame.place(x=20, y=20)
        
        self.contador_label = tk.Label(
            self.contador_frame,
            text=f"{self.preguntas_correctas}/{self.preguntas_minimas}",
            font=(self.config['interfaz']['fuente_contador'], 
                  self.config['interfaz']['tamaño_fuente_contador'], 'bold'),
            fg='#FFD700',
            bg='#F0F8FF',
            relief='raised',
            bd=3,
            padx=15,
            pady=8
        )
        self.contador_label.pack()
        
        # Área de pruebas
        test_frame = tk.LabelFrame(
            self.root,
            text="🔧 Pruebas de Funcionalidad",
            font=('Arial', 14, 'bold'),
            bg='#F0F8FF'
        )
        test_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Test del contador
        contador_test_frame = tk.LabelFrame(
            test_frame,
            text="📊 Test del Contador Animado",
            font=('Arial', 12, 'bold'),
            bg='#F0F8FF'
        )
        contador_test_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(
            contador_test_frame,
            text=f"Preguntas mínimas configuradas: {self.preguntas_minimas}",
            font=('Arial', 11),
            bg='#F0F8FF'
        ).pack(anchor='w', padx=10, pady=5)
        
        btn_incrementar = tk.Button(
            contador_test_frame,
            text="➕ Simular Respuesta Correcta",
            font=('Arial', 11, 'bold'),
            bg='#90EE90',
            command=self.simular_respuesta_correcta
        )
        btn_incrementar.pack(pady=10)
        
        btn_reset = tk.Button(
            contador_test_frame,
            text="🔄 Resetear Contador",
            font=('Arial', 11),
            bg='#FFB6C1',
            command=self.resetear_contador
        )
        btn_reset.pack(pady=5)
        
        # Test del sonido typing
        typing_test_frame = tk.LabelFrame(
            test_frame,
            text="🔊 Test del Sonido de Typing",
            font=('Arial', 12, 'bold'),
            bg='#F0F8FF'
        )
        typing_test_frame.pack(fill='x', padx=10, pady=10)
        
        tk.Label(
            typing_test_frame,
            text=f"Sonido de typing habilitado: {self.config['interfaz']['sonido_typing']}",
            font=('Arial', 11),
            bg='#F0F8FF'
        ).pack(anchor='w', padx=10, pady=5)
        
        tk.Label(
            typing_test_frame,
            text="Escribe en el campo de abajo para probar el sonido:",
            font=('Arial', 11),
            bg='#F0F8FF'
        ).pack(anchor='w', padx=10, pady=5)
        
        self.entry_test = tk.Entry(
            typing_test_frame,
            font=('Comic Sans MS', 14),
            width=40
        )
        self.entry_test.pack(pady=10)
        self.entry_test.bind('<KeyPress>', self.reproducir_sonido_typing)
        
        # Test de configuración
        config_test_frame = tk.LabelFrame(
            test_frame,
            text="⚙️ Verificación de Configuración",
            font=('Arial', 12, 'bold'),
            bg='#F0F8FF'
        )
        config_test_frame.pack(fill='x', padx=10, pady=10)
        
        configuraciones = [
            f"Preguntas mínimas para salir: {self.config['seguridad']['preguntas_minimas_para_salir']}",
            f"Sonido de typing: {self.config['interfaz']['sonido_typing']}",
            f"Fuente del contador: {self.config['interfaz']['fuente_contador']}",
            f"Tamaño fuente contador: {self.config['interfaz']['tamaño_fuente_contador']}"
        ]
        
        for config_text in configuraciones:
            tk.Label(
                config_test_frame,
                text=f"✅ {config_text}",
                font=('Arial', 10),
                bg='#F0F8FF',
                fg='#006400'
            ).pack(anchor='w', padx=10, pady=2)
        
        # Iniciar animación del contador
        self.animar_contador()
        
    def simular_respuesta_correcta(self):
        """Simula una respuesta correcta para probar el contador"""
        self.preguntas_correctas += 1
        self.reproducir_sonido_correcto()
        
        if self.preguntas_correctas >= self.preguntas_minimas:
            messagebox.showinfo(
                "¡Máximo Alcanzado!",
                f"🎉 ¡Has alcanzado {self.preguntas_minimas} preguntas correctas!\n"
                "El botón de salir se habilitaría ahora."
            )
    
    def resetear_contador(self):
        """Resetea el contador para probar de nuevo"""
        self.preguntas_correctas = 0
        
    def animar_contador(self):
        """Anima el contador con efecto brillante"""
        # Colores para la animación
        colores = ['#FFD700', '#FFA500', '#FF8C00', '#FFD700', '#FFFF00', '#FFD700']
        color_actual = colores[int(time.time() * 2) % len(colores)]
        
        # Actualizar texto y color
        texto_contador = f"{self.preguntas_correctas}/{self.preguntas_minimas}"
        self.contador_label.config(text=texto_contador, fg=color_actual)
        
        # Efecto especial cuando se alcanza el máximo
        if self.preguntas_correctas >= self.preguntas_minimas:
            self.contador_label.config(
                fg='#00FF00',
                font=(self.config['interfaz']['fuente_contador'], 
                      self.config['interfaz']['tamaño_fuente_contador'] + 2, 'bold'),
                relief='solid',
                bd=4
            )
            # Efecto de parpadeo
            if int(time.time() * 4) % 2:
                self.contador_label.config(bg='#90EE90')
            else:
                self.contador_label.config(bg='#F0F8FF')
        else:
            self.contador_label.config(
                font=(self.config['interfaz']['fuente_contador'], 
                      self.config['interfaz']['tamaño_fuente_contador'], 'bold'),
                relief='raised',
                bd=3,
                bg='#F0F8FF'
            )
        
        # Programar siguiente animación
        self.root.after(500, self.animar_contador)
    
    def reproducir_sonido_typing(self, event=None):
        """Reproduce sonido de typing"""
        if not self.config['interfaz']['sonido_typing']:
            return
            
        try:
            import winsound
            
            def reproducir_async():
                winsound.Beep(400, 50)
            
            threading.Thread(target=reproducir_async, daemon=True).start()
            
        except ImportError:
            pass
    
    def reproducir_sonido_correcto(self):
        """Reproduce sonido de respuesta correcta"""
        try:
            import winsound
            
            def reproducir_async():
                winsound.Beep(800, 200)
            
            threading.Thread(target=reproducir_async, daemon=True).start()
            
        except ImportError:
            pass
    
    def ejecutar(self):
        """Ejecuta la aplicación de prueba"""
        self.root.mainloop()

def main():
    """Función principal"""
    print("🧪 Iniciando test de nuevas funciones...")
    
    # Verificar que existe config.json
    if not Path('config.json').exists():
        print("❌ Error: No se encontró config.json")
        return
    
    app = TestNuevasFunciones()
    app.ejecutar()

if __name__ == "__main__":
    main()
